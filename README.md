# 🛍️ Pasabuy Pal

A comprehensive web application for managing pasabuy (personal shopping) business operations. Built with Next.js, TypeScript, Prisma, and modern UI components.

## ✨ Features

### 🏪 Store Management
- Add and manage store codes (SM, Ayala, etc.)
- Store-specific buy lists
- Track orders by store location

### 👥 Customer Management
- Manage customer profiles
- Customer-specific order tracking
- Status indicators for each customer

### 📦 Order Management
- Add orders with images
- Price calculations with manual customer price entry
- Quantity tracking
- Status management (bought/not bought, packed/not packed)

### 📋 Buy Lists
- Overview of all orders to buy
- Store-specific shopping lists
- Real-time status updates
- Quick "Mark as Bought" functionality

### 📦 Packing Workflow
- Customer-specific packing lists
- Track packing status
- Bulk packing operations
- Visual progress indicators

### 📱 Progressive Web App (PWA)
- Mobile-responsive design
- Offline capabilities
- App-like experience on mobile devices

## 🚀 Tech Stack

- **Frontend**: Next.js 15, React, TypeScript
- **UI**: Tailwind CSS, shadcn/ui components
- **Database**: SQLite (development) / PostgreSQL (production)
- **ORM**: Prisma
- **State Management**: Zustand
- **Forms**: React Hook Form
- **Icons**: Lucide React
- **PWA**: next-pwa

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/YOUR_USERNAME/pasabuy-pal.git
   cd pasabuy-pal
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```

   Update the `.env` file with your Appwrite credentials:
   ```env
   NEXT_PUBLIC_APP_URL="http://localhost:3001"
   NEXT_PUBLIC_APPWRITE_ENDPOINT="https://cloud.appwrite.io/v1"
   NEXT_PUBLIC_APPWRITE_PROJECT_ID="your-project-id"
   APPWRITE_API_KEY="your-api-key"
   APPWRITE_DATABASE_ID="your-database-id"
   APPWRITE_STORAGE_BUCKET_ID="your-bucket-id"
   ```

4. **Set up the Appwrite database**
   ```bash
   npm run appwrite:setup
   ```

5. **Verify the migration**
   ```bash
   npm run appwrite:verify
   ```

6. **Start the development server**
   ```bash
   npm run dev
   ```

7. **Open your browser**
   Navigate to [http://localhost:3001](http://localhost:3001)

## 📁 Project Structure

```
pasabuy-pal/
├── src/
│   ├── app/                    # Next.js app router
│   │   ├── api/               # API routes
│   │   ├── buy-list/          # Buy list pages
│   │   ├── orders/            # Order management
│   │   ├── packing/           # Packing workflow
│   │   ├── customers/         # Customer management
│   │   └── stores/            # Store management
│   ├── components/            # Reusable components
│   │   ├── forms/             # Form components
│   │   └── ui/                # UI components (shadcn/ui)
│   └── lib/                   # Utilities and configurations
├── scripts/                   # Setup and migration scripts
├── public/                    # Static assets
└── uploads/                   # User uploaded files
```

## 🗄️ Database Schema

The application uses Appwrite as a complete backend solution with these main collections:

- **Stores**: Store locations with user isolation
- **Customers**: Customer management with user-specific data
- **Orders**: Product orders with pricing and status tracking
- **Invoices**: Invoice management and tracking

## 🔧 API Endpoints

### Appwrite API Routes
- `GET /api/appwrite/store-codes` - List user's store codes
- `POST /api/appwrite/store-codes` - Create new store code
- `GET /api/appwrite/customers` - List user's customers
- `POST /api/appwrite/customers` - Create new customer
- `GET /api/appwrite/orders` - List user's orders (with filtering)
- `POST /api/appwrite/orders` - Create new order
- `PUT /api/appwrite/orders/[id]` - Update order
- `DELETE /api/appwrite/orders/[id]` - Delete order

### File Upload (Appwrite Storage)
- `POST /api/appwrite/upload` - Upload files to Appwrite Storage
- `DELETE /api/appwrite/upload` - Delete files from Appwrite Storage

### Authentication
- Built-in Appwrite authentication with user registration and login

## 🚀 Deployment

### Vercel (Recommended)

1. **Push to GitHub**
2. **Connect to Vercel**
3. **Set environment variables**
4. **Deploy**

### Docker

```bash
# Build the image
docker build -t pasabuy-pal .

# Run the container
docker run -p 3000:3000 pasabuy-pal
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- UI components from [shadcn/ui](https://ui.shadcn.com/)
- Icons from [Lucide](https://lucide.dev/)
- Backend services by [Appwrite](https://appwrite.io/)

---

Made with ❤️ for the pasabuy community
