'use client'

import { useEffect, useState, useCallback } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { LuArrowLeft, LuPackage, LuPencil } from 'react-icons/lu'
import Link from 'next/link'
import { CustomerOrderCard } from '@/components/orders/customer-order-card'
import { Pagination } from '@/components/ui/pagination'
import { PaginationInfo } from '@/components/ui/pagination-info'
import { useScrollToTop } from '@/hooks/use-scroll'

type Order = {
  id: number
  productName: string
  quantity: number
  usageUnit?: string | null
  comment?: string | null
  imageFilename: string | null
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  resellerPrice: number // For compatibility
  isBought: boolean
  packingStatus: string
  storeCode?: {
    id: number
    code: string
    name: string | null
  } | null
  customer?: {
    id: number
    name: string
  } | null
  createdAt: string
  updatedAt: string
}

type Customer = {
  id: number
  name: string
  customerNumber?: string
  customerType: string
  status: string
  segment: string
  loyaltyTier: string
  email?: string
  phone?: string
  alternatePhone?: string
  website?: string
  address?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
  businessName?: string
  taxId?: string
  businessType?: string
  preferredDeliveryMethod?: string
  preferredPaymentMethod?: string
  creditLimit: number
  paymentTerms: number
  discountRate: number
  assignedSalesRep?: string
  accountManager?: string
  referredBy?: string
  notes?: string
  internalNotes?: string
  totalOrders: number
  totalSpent: number
  averageOrderValue: number
  firstOrderDate?: string
  lastOrderDate?: string
  lastContactDate?: string
  _count: {
    orders: number
    toBuy: number
    toPack: number
  }
}

// Helper functions for styling
const getLoyaltyTierColor = (tier: string) => {
  switch (tier) {
    case 'DIAMOND': return 'text-purple-600 bg-purple-50 border-purple-200'
    case 'PLATINUM': return 'text-gray-600 bg-gray-50 border-gray-200'
    case 'GOLD': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    case 'SILVER': return 'text-gray-500 bg-gray-50 border-gray-200'
    case 'BRONZE': return 'text-orange-600 bg-orange-50 border-orange-200'
    default: return 'text-gray-500 bg-gray-50 border-gray-200'
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'ACTIVE': return 'text-green-600 bg-green-50 border-green-200'
    case 'INACTIVE': return 'text-gray-500 bg-gray-50 border-gray-200'
    case 'SUSPENDED': return 'text-red-600 bg-red-50 border-red-200'
    case 'PROSPECT': return 'text-blue-600 bg-blue-50 border-blue-200'
    default: return 'text-gray-500 bg-gray-50 border-gray-200'
  }
}

export default function CustomerDetailPage() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const customerId = parseInt(params.id as string)

  // Add refresh key to force re-fetch when needed
  const [refreshKey, setRefreshKey] = useState(0)

  const [customer, setCustomer] = useState<Customer | null>(null)
  const [isLoadingCustomer, setIsLoadingCustomer] = useState(true)
  const [customerError, setCustomerError] = useState<string | null>(null)

  // Automatically scroll to top when page loads
  useScrollToTop()

  // Custom state for customer-specific orders (bypassing useFilters for initial load)
  const [customerOrders, setCustomerOrders] = useState<Order[]>([])
  const [isLoadingOrders, setIsLoadingOrders] = useState(true)
  const [ordersError, setOrdersError] = useState<string | null>(null)



  // Tab-specific pagination state
  const [currentPages, setCurrentPages] = useState({
    'to-buy': 1,
    'to-pack': 1,
    'packed': 1
  })
  const [itemsPerPage] = useState(10)

  // Create a refresh function that can be called manually
  const refreshData = useCallback(async () => {
    try {
      setIsLoadingCustomer(true)
      setIsLoadingOrders(true)
      setCustomerError(null)
      setOrdersError(null)

      // Fetch customer data and orders in parallel
      const [customerResponse, ordersResponse] = await Promise.all([
        fetch(`/api/appwrite/customers/${customerId}`),
        fetch(`/api/appwrite/orders?customers.include=${customerId}`)
      ])

      if (!customerResponse.ok) {
        throw new Error('Failed to fetch customer data')
      }

      if (!ordersResponse.ok) {
        throw new Error('Failed to fetch orders data')
      }

      const [customerData, ordersData] = await Promise.all([
        customerResponse.json(),
        ordersResponse.json()
      ])

      setCustomer(customerData)

      // Handle both legacy and advanced response formats for orders
      let orders: Order[] = []
      if (Array.isArray(ordersData)) {
        orders = ordersData
      } else if (ordersData && ordersData.data && Array.isArray(ordersData.data)) {
        orders = ordersData.data
      }

      setCustomerOrders(orders)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setCustomerError(errorMessage)
      setOrdersError(errorMessage)
    } finally {
      setIsLoadingCustomer(false)
      setIsLoadingOrders(false)
    }
  }, [customerId])

  // Fetch customer data and customer-specific orders
  useEffect(() => {
    if (customerId) {
      refreshData()
    }
  }, [customerId, refreshKey, refreshData])

  // Check for refresh parameter from edit page
  useEffect(() => {
    const refreshParam = searchParams.get('refresh')
    if (refreshParam === 'true') {
      // Remove the refresh parameter from URL
      const newUrl = new URL(window.location.href)
      newUrl.searchParams.delete('refresh')
      window.history.replaceState({}, '', newUrl.toString())

      // Trigger refresh
      setRefreshKey(prev => prev + 1)
    }
  }, [searchParams])

  // Listen for window focus to refresh data when returning to page
  useEffect(() => {
    const handleFocus = () => {
      // Only refresh if the page has been away for more than 5 seconds
      const lastRefresh = sessionStorage.getItem(`customer-${customerId}-last-refresh`)
      const now = Date.now()
      if (!lastRefresh || now - parseInt(lastRefresh) > 5000) {
        setRefreshKey(prev => prev + 1)
        sessionStorage.setItem(`customer-${customerId}-last-refresh`, now.toString())
      }
    }

    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [customerId])



  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
    }).format(amount)
  }

  // Use customer orders directly (no additional filtering needed on detail page)
  const filteredCustomerOrders = customerOrders

  const toBuyOrders = filteredCustomerOrders.filter((order: Order) => !order.isBought)
  const toPackOrders = filteredCustomerOrders.filter((order: Order) => order.isBought && order.packingStatus === 'Not Packed')
  const packedOrders = filteredCustomerOrders.filter((order: Order) => order.isBought && order.packingStatus === 'Packed')

  // Pagination logic for each tab
  const getPaginatedOrders = (ordersList: Order[], tabKey: keyof typeof currentPages) => {
    const currentPage = currentPages[tabKey]
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    return ordersList.slice(startIndex, endIndex)
  }

  const paginatedToBuyOrders = getPaginatedOrders(toBuyOrders, 'to-buy')
  const paginatedToPackOrders = getPaginatedOrders(toPackOrders, 'to-pack')
  const paginatedPackedOrders = getPaginatedOrders(packedOrders, 'packed')

  // Handle page changes for specific tabs
  const handlePageChange = (tabKey: keyof typeof currentPages, page: number) => {
    setCurrentPages(prev => ({
      ...prev,
      [tabKey]: page
    }))
  }

  // Reset pagination when filters change
  useEffect(() => {
    setCurrentPages({
      'to-buy': 1,
      'to-pack': 1,
      'packed': 1
    })
  }, [])



  const totalValue = filteredCustomerOrders.reduce((sum: number, order: Order) => sum + (order.quantity * order.customerPrice), 0)
  const totalProfit = filteredCustomerOrders.reduce((sum: number, order: Order) => sum + (order.quantity * order.pasabuyFee), 0)

  if (isLoadingCustomer || isLoadingOrders) {
    return (
      <div className="space-y-6 py-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  if (customerError || ordersError || !customer) {
    return (
      <div className="space-y-6 py-4">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.back()}
          >
            <LuArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-semibold tracking-tight">Customer Not Found</h1>
        </div>

        <Card className="p-6">
          <div className="text-center">
            <h2 className="text-sm font-medium text-red-600">Error</h2>
            <p className="text-sm text-muted-foreground mt-1">{customerError || ordersError}</p>
            <Button onClick={() => router.back()} className="mt-4">
              Go Back
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6 py-4">
      {/* Enhanced Header */}
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.push('/customers')}
          >
            <LuArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex-1">
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-semibold tracking-tight">{customer.name}</h1>
              {customer.customerNumber && (
                <Badge variant="outline" className="text-xs">
                  {customer.customerNumber}
                </Badge>
              )}
              <Badge
                variant="outline"
                className={`text-xs ${getStatusColor(customer.status)}`}
              >
                {customer.status}
              </Badge>
              {customer.loyaltyTier && (
                <Badge
                  variant="outline"
                  className={`text-xs ${getLoyaltyTierColor(customer.loyaltyTier)}`}
                >
                  {customer.loyaltyTier}
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
              <span>{customer._count.orders} total orders</span>
              <span>₱{customer.totalSpent.toLocaleString()} spent</span>
              {customer.averageOrderValue > 0 && (
                <span>₱{customer.averageOrderValue.toLocaleString()} avg</span>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Link href={`/customers/${customer.id}/edit`}>
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <LuPencil className="h-4 w-4" />
                Edit
              </Button>
            </Link>
          </div>
        </div>

        {/* Customer Info Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Contact Information */}
          <Card className="p-4">
            <h3 className="font-medium text-sm mb-3">Contact Information</h3>
            <div className="space-y-2 text-sm">
              {customer.email && (
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">Email:</span>
                  <a href={`mailto:${customer.email}`} className="text-blue-600 hover:underline">
                    {customer.email}
                  </a>
                </div>
              )}
              {customer.phone && (
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">Phone:</span>
                  <a href={`tel:${customer.phone}`} className="text-blue-600 hover:underline">
                    {customer.phone}
                  </a>
                </div>
              )}
              {customer.alternatePhone && (
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">Alt Phone:</span>
                  <a href={`tel:${customer.alternatePhone}`} className="text-blue-600 hover:underline">
                    {customer.alternatePhone}
                  </a>
                </div>
              )}
              {customer.website && (
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">Website:</span>
                  <a href={customer.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                    {customer.website}
                  </a>
                </div>
              )}
            </div>
          </Card>

          {/* Address Information */}
          <Card className="p-4">
            <h3 className="font-medium text-sm mb-3">Address</h3>
            <div className="text-sm">
              {customer.address ? (
                <div className="space-y-1">
                  <div>{customer.address}</div>
                  {(customer.city || customer.state || customer.postalCode) && (
                    <div>
                      {[customer.city, customer.state, customer.postalCode].filter(Boolean).join(', ')}
                    </div>
                  )}
                  {customer.country && customer.country !== 'Philippines' && (
                    <div>{customer.country}</div>
                  )}
                </div>
              ) : (
                <span className="text-muted-foreground">No address provided</span>
              )}
            </div>
          </Card>

          {/* Business Information */}
          {customer.customerType !== 'INDIVIDUAL' && (
            <Card className="p-4">
              <h3 className="font-medium text-sm mb-3">Business Information</h3>
              <div className="space-y-2 text-sm">
                {customer.businessName && (
                  <div className="flex items-center gap-2">
                    <span className="text-muted-foreground">Business:</span>
                    <span>{customer.businessName}</span>
                  </div>
                )}
                {customer.taxId && (
                  <div className="flex items-center gap-2">
                    <span className="text-muted-foreground">Tax ID:</span>
                    <span>{customer.taxId}</span>
                  </div>
                )}
                {customer.businessType && (
                  <div className="flex items-center gap-2">
                    <span className="text-muted-foreground">Type:</span>
                    <span>{customer.businessType}</span>
                  </div>
                )}
              </div>
            </Card>
          )}
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-red-600">{customer._count.toBuy}</p>
            <p className="text-sm text-muted-foreground">To Buy</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-orange-600">{customer._count.toPack}</p>
            <p className="text-sm text-muted-foreground">To Pack</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">{formatCurrency(totalValue)}</p>
            <p className="text-sm text-muted-foreground">Total Value</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">{formatCurrency(totalProfit)}</p>
            <p className="text-sm text-muted-foreground">Total Profit</p>
          </div>
        </Card>
      </div>

      {/* Items Tabs */}
      <Tabs defaultValue="to-buy" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="to-buy">To Buy ({toBuyOrders.length})</TabsTrigger>
          <TabsTrigger value="to-pack">To Pack ({toPackOrders.length})</TabsTrigger>
          <TabsTrigger value="packed">Packed ({packedOrders.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="to-buy" className="space-y-4">
          {toBuyOrders.length === 0 ? (
            <Card className="p-6">
              <div className="text-center">
                <LuPackage className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="text-sm font-medium mt-2">No orders to buy</h3>
                <p className="text-sm text-muted-foreground mt-1">All orders have been purchased.</p>
              </div>
            </Card>
          ) : (
            <>
              <div className="grid gap-2 sm:gap-2.5">
                {paginatedToBuyOrders.map((order) => (
                  <CustomerOrderCard
                    key={order.id}
                    item={order}
                    displayOnly={true}
                  />
                ))}
              </div>

              {/* Pagination for To Buy */}
              {toBuyOrders.length > itemsPerPage && (
                <div className="mt-4 space-y-3">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                    <PaginationInfo
                      currentPage={currentPages['to-buy']}
                      totalPages={Math.ceil(toBuyOrders.length / itemsPerPage)}
                      totalItems={toBuyOrders.length}
                      itemsPerPage={itemsPerPage}
                      className="order-2 sm:order-1"
                    />
                    <Pagination
                      currentPage={currentPages['to-buy']}
                      totalPages={Math.ceil(toBuyOrders.length / itemsPerPage)}
                      onPageChange={(page) => handlePageChange('to-buy', page)}
                      className="order-1 sm:order-2"
                    />
                  </div>
                </div>
              )}
            </>
          )}
        </TabsContent>

        <TabsContent value="to-pack" className="space-y-4">
          {toPackOrders.length === 0 ? (
            <Card className="p-6">
              <div className="text-center">
                <LuPackage className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="text-sm font-medium mt-2">No orders to pack</h3>
                <p className="text-sm text-muted-foreground mt-1">All bought orders have been packed.</p>
              </div>
            </Card>
          ) : (
            <>
              <div className="grid gap-2 sm:gap-2.5">
                {paginatedToPackOrders.map((order) => (
                  <CustomerOrderCard
                    key={order.id}
                    item={order}
                    displayOnly={true}
                  />
                ))}
              </div>

              {/* Pagination for To Pack */}
              {toPackOrders.length > itemsPerPage && (
                <div className="mt-4 space-y-3">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                    <PaginationInfo
                      currentPage={currentPages['to-pack']}
                      totalPages={Math.ceil(toPackOrders.length / itemsPerPage)}
                      totalItems={toPackOrders.length}
                      itemsPerPage={itemsPerPage}
                      className="order-2 sm:order-1"
                    />
                    <Pagination
                      currentPage={currentPages['to-pack']}
                      totalPages={Math.ceil(toPackOrders.length / itemsPerPage)}
                      onPageChange={(page) => handlePageChange('to-pack', page)}
                      className="order-1 sm:order-2"
                    />
                  </div>
                </div>
              )}
            </>
          )}
        </TabsContent>

        <TabsContent value="packed" className="space-y-4">
          {packedOrders.length === 0 ? (
            <Card className="p-6">
              <div className="text-center">
                <LuPackage className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="text-sm font-medium mt-2">No packed orders</h3>
                <p className="text-sm text-muted-foreground mt-1">Orders will appear here once they are packed.</p>
              </div>
            </Card>
          ) : (
            <>
              <div className="grid gap-2 sm:gap-2.5">
                {paginatedPackedOrders.map((order) => (
                  <CustomerOrderCard
                    key={order.id}
                    item={order}
                    displayOnly={true}
                  />
                ))}
              </div>

              {/* Pagination for Packed */}
              {packedOrders.length > itemsPerPage && (
                <div className="mt-4 space-y-3">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                    <PaginationInfo
                      currentPage={currentPages['packed']}
                      totalPages={Math.ceil(packedOrders.length / itemsPerPage)}
                      totalItems={packedOrders.length}
                      itemsPerPage={itemsPerPage}
                      className="order-2 sm:order-1"
                    />
                    <Pagination
                      currentPage={currentPages['packed']}
                      totalPages={Math.ceil(packedOrders.length / itemsPerPage)}
                      onPageChange={(page) => handlePageChange('packed', page)}
                      className="order-1 sm:order-2"
                    />
                  </div>
                </div>
              )}
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
